package server

import (
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"path"
	"time"

	"github.com/Depado/ginprom"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/onwa/app/api/routes"

	"github.com/onwa/docs"
	"github.com/onwa/pkg/config"
	"github.com/onwa/pkg/database"
	"github.com/onwa/pkg/domains/license"
	"github.com/onwa/pkg/domains/other"
	"github.com/onwa/pkg/domains/user"
	"github.com/onwa/pkg/domains/wp"
	"github.com/onwa/pkg/embed"
	"github.com/onwa/pkg/middleware"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
)

var (
	swaggerUser string
	swaggerPass string
)

func LaunchHttpServer(appc config.App, allows config.Allows) {
	log.Println("Starting HTTP Server...")
	gin.SetMode(gin.ReleaseMode)

	app := gin.New()
	app.Use(gin.LoggerWithFormatter(func(log gin.LogFormatterParams) string {
		return fmt.Sprintf("[%s] - %s \"%s %s %s %d %s\"\n",
			log.TimeStamp.Format("2006-01-02 15:04:05"),
			log.ClientIP,
			log.Method,
			log.Path,
			log.Request.Proto,
			log.StatusCode,
			log.Latency,
		)
	}))
	app.Static("/bd928992-5e86-44c7-b625-5b580cb6ce21", "./uploads")
	app.Use(gin.Recovery())
	app.Use(otelgin.Middleware(appc.Name))
	app.Use(middleware.ClaimIp())
	app.Use(cors.New(cors.Config{
		AllowMethods:     allows.Methods,
		AllowHeaders:     allows.Headers,
		AllowOrigins:     allows.Origins,
		AllowCredentials: false,
		MaxAge:           12 * time.Hour,
	}))

	p := ginprom.New(
		ginprom.Engine(app),
		ginprom.Subsystem("gin"),
		ginprom.Path("/metrics"),
		ginprom.Ignore("/swagger/*any"),
	)
	app.Use(p.Instrument())

	db := database.DBClient()
	api := app.Group("/api/v1")

	user_repo := user.NewRepo(db)
	user_service := user.NewService(user_repo)

	wp_repo := wp.NewRepo(db)
	wp_service := wp.NewService(wp_repo)

	other_repo := other.NewRepo(db)
	other_service := other.NewService(other_repo)

	license_repo := license.NewRepo(db)
	license_service := license.NewService(license_repo)

	routes.VersionRoutes(api)
	routes.WPRoutes(api, wp_service)
	routes.AuthRoutes(api, user_service)
	routes.LicenseRoutes(api, license_service)
	routes.OtherRoutes(api, other_service)

	app.GET("/docs", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "docs/index.html")
	})

	if os.Getenv("SWAGGER_USER") != "" {
		swaggerUser = os.Getenv("SWAGGER_USER")
	} else {
		swaggerUser = "onwa"
	}
	if os.Getenv("SWAGGER_PASS") != "" {
		swaggerPass = os.Getenv("SWAGGER_PASS")
	} else {
		swaggerPass = "onwa"
	}

	docs.SwaggerInfo.Host = "https://onwa.com/api/v1"
	docs.SwaggerInfo.Version = os.Getenv("APP_VERSION")
	app.GET("/docs/*any",
		gin.BasicAuth(gin.Accounts{
			swaggerUser: swaggerPass,
		}),
		ginSwagger.WrapHandler(swaggerFiles.Handler),
	)

	app.GET("/assets/*filepath", func(c *gin.Context) {
		c.FileFromFS(path.Join("/dist/", c.Request.URL.Path), http.FS(embed.StaticsFS()))
	})
	app.Any("/terms.html", func(c *gin.Context) {
		c.FileFromFS("dist/terms.html", http.FS(embed.StaticsFS()))
	})
	app.Any("/contact.html", func(c *gin.Context) {
		c.FileFromFS("dist/contact.html", http.FS(embed.StaticsFS()))
	})
	app.Any("/privacy.html", func(c *gin.Context) {
		c.FileFromFS("dist/privacy.html", http.FS(embed.StaticsFS()))
	})
	app.Any("/", func(c *gin.Context) {
		c.FileFromFS("dist/", http.FS(embed.StaticsFS()))
	})
	app.NoRoute(func(c *gin.Context) {
		c.FileFromFS("dist/", http.FS(embed.StaticsFS()))
	})

	fmt.Println("Server is running on port " + appc.Port)
	app.Run(net.JoinHostPort(appc.Host, appc.Port))
}
